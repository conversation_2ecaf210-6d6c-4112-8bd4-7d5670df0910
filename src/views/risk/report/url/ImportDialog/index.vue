<template>
  <component
    :is="embedded ? 'div' : 'el-dialog'"
    class="url-detail"
    title="导入附件"
    :visible.sync="visible"
    width="800px"
    append-to-body
    destroy-on-close
    @closed="onClosed"
  >
    <span
      v-if="params.taskType == '7'"
      style="color: red"
      class="text-center py-3 text-base font-bold absolute inset-x-32 top-0"
    >
      如接入态势感知 则必须上传eml附件和接入主机清单附件
    </span>
    <div v-if="!hiddenUpload" v-loading="uploadLoading" element-loading-spinner="el-icon-loading" :element-loading-text="loadingText" class="px-5 pt-4 flex space-x-4 overflow-hidden">
      <div v-for="(item, index) of uploadModel" :key="`${item.label}-${index}`" class="flex-none !w-[360px]">
        <el-upload
          ref="uploadRef"
          :accept="item.accept"
          :headers="uploadProps.headers"
          :action="item.action"
          :data="item.data"
          :before-upload="beforeUpload"
          :on-success="item.onSuccess || onSuccess"
          :on-progress="onProgress"
          :multiple="item.multiple"
          :limit="item.limit"
          drag
        >
          <div class="text-center py-3 text-base font-bold absolute inset-x-0 top-0">{{
            item.label
          }}</div>
          <i class="el-icon-upload !pt-4" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>

          <template #tip>
            <div class="">
              <div v-if="item.tipKey" class="text-xs pt-2" style="color: red">
                {{ tips[item.tipKey] }}
              </div>
              <div v-if="item.templateKey" class="pt-2">
                <el-link
                  type="info"
                  style="color: green"
                  @click="downloadTemplate(item.templateKey)"
                >
                  {{ item.templateName || '点击下载模板' }}
                </el-link>
              </div>
            </div>
          </template>
        </el-upload>
      </div>
    </div>

    <el-divider v-if="!hiddenUpload" content-position="left" class="!mt-8">已上传报告列表</el-divider>

    <!-- [1, 3, 4, 5, 6].includes(Number(urlDetail.taskType)) && -->
    <url-index
      v-if="visible && $checkPermi(['risk:report:url:list'])"
      ref="urlIndexRef"
      v-bind="{ urlDetail, excludeColumns, includeColumns, disabledRemove, fileColumnName }"
      :class="{'page-main--flat': pageMainLess}"
      v-on="$listeners"
    />

    <!-- <template #footer>
      <el-button @click="close"> 取消 </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="uploadProps.isUploading">
        确认
      </el-button>
    </template> -->
  </component>
</template>

<script>
import { getToken } from '@/utils/auth'
import UrlIndex from '../index.vue'

import request from '@/utils/request.js'
import { debounce, isEqual } from 'lodash-es'

export default {
  components: {
    UrlIndex
  },
  props: {
    embedded: {
      type: Boolean,
      default: false
    },
    hiddenUpload: {
      type: Boolean,
      default: false
    },
    disabledRemove: {
      type: Boolean,
      default: false
    },
    pageMainLess: {
      type: Boolean,
      default: false
    },
    excludeColumns: {
      type: Array,
      default: () => []
    },
    includeColumns: {
      type: Array,
      default: () => []
    },
    fileColumnName: {
      type: String,
      default: void 0
    }
  },
  data() {
    const tipModel = [
      'task.import.report.prompt',
      'task.import.report.ladingData',
      'task.import.report.system',
      'task.import.report.line',
      'task.import.report.password',
      'task.import.report.image',
      'task.import.report.fiveSync',
      'task.import.report.fiveSyncEml',
      'task.import.report.app'
    ]

    return {
      visible: false,

      uploadLoading: false,

      tipModel,
      tips: {
        ...tipModel.reduce((obj, item) => {
          obj[item] = ''
          return obj
        }, {})
      },
      tipMap: {
        1: 'prompt',
        3: 'system',
        4: 'line',
        5: 'password',
        6: 'image',
        7: 'fiveSync',
        8: 'fiveSyncEml'
      },

      uploadProps: {
        title: '导入报告',
        isUploading: false,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadTaskRiskScanZip',
        data: null,
        loading: false,
        tip: ''
      },

      params: {},

      loadingText: ''
    }
  },
  created() {
    this.onSuccess = debounce(this.onSuccess, 1000, { trailing: false, leading: true })
  },
  watch: {
    '$attrs.params': {
      handler(val, oldVal) {
        if (isEqual(val, oldVal)) {
          return false
        }

        if (this.embedded) {
          this.open({ params: this.$attrs.params })
        }
      },
      immediate: true
    }
  },
  computed: {
    urlDetail() {
      if (!this.params) {
        return {}
      }

      const value = {
        taskId: this.params.id || this.params.taskId,
        taskType: this.params.taskType
      }

      if (this.params.sendId) {
        Object.assign(value, { sendId: this.params.sendId })
      }

      if (this.params.reportType) {
        Object.assign(value, { reportType: this.params.reportType })
      }

      return value
    },
    scopeTip() {
      return this.tipMap[this.params.taskType]
    },
    uploadModel() {
      const value = []

      if (
        (['1', '3'].includes(String(this.params.taskType)) && this.$checkPermi(['risk:report:task:uploadRiskScanZip'])) ||
        (['4', '5'].includes(String(this.params.taskType)) && this.$checkPermi(['risk:report:line:pwdtask:uploadRiskScanZip'])) ||
        (['9'].includes(String(this.params.taskType)) && this.$checkPermi(['risk:business:task:upgrade:generalRule']))
      ) {
        value.push({
          label: '安全问题附件',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadTaskRiskScanZip',
          // accept: '.zip, .rar',
          tipKey: `task.import.report.${this.scopeTip}`,
          templateKey: 'risk.report.template',
          data: {
            ...this.urlDetail
          }
        })
      }

      if (
        (['6'].includes(String(this.params.taskType)) && this.$checkPermi(['risk:report:task:uploadRiskScanZip']))
      ) {
        value.push({
          label: '安全问题附件',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadTaskRiskScanZip',
          // accept: '.zip, .rar',
          tipKey: 'task.import.report.image',
          templateKey: 'risk.report.template.image',
          templateName: '点击下载样例',
          data: {
            ...this.urlDetail
          }
        })
      }

      const ladingSign = ['3', '4', '5'].includes(String(this.params.taskType)) &&
          this.$checkPermi(['risk:report:lading:data:import']) &&
          ['13', void 0].includes(this.params.taskStatus)

      const collectionLadingSign = ['1'].includes(String(this.params.taskType)) && (this.params.executeScriptFlag === '3')

      if (
        ladingSign || collectionLadingSign
      ) {
        value.push({
          label: '提单数据',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadLadingData',
          accept: '.xlsx, .xls, .png, .jpg',
          tipKey: 'task.import.report.ladingData',
          templateKey: 'risk_lading_data_url',
          data: {
            ...this.urlDetail,
            type: 3
          }
        })
      }

      if (
        ['6'].includes(String(this.params.taskType)) &&
          this.$checkPermi(['risk:report:lading:data:import']) &&
          this.params.taskStatus
      ) {
        value.push({
          label: '提单数据',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadLadingData',
          accept: '.xlsx, .xls, .png, .jpg',
          tipKey: 'task.import.report.ladingData',
          templateKey: 'images_lading_data_url',
          data: {
            ...this.urlDetail,
            type: 3
          }
        })
      }

      if (['7'].includes(String(this.params.taskType)) && this.$checkPermi(['five:sync:upload:attachment'])) {
        value.push({
          label: '证明材料附件EML',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadLadingData',
          accept: '.eml',
          tipKey: 'task.import.report.fiveSyncEml',
          templateKey: '',
          data: {
            ...this.urlDetail,
            type: 3
          }
        })
      }

      if (['7'].includes(String(this.params.taskType)) && this.params.taskStatus && this.$checkPermi(['five:sync:upload:attachment'])) {
        value.push({
          label: '态势感知接入清单附件',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadLadingData',
          accept: '.xlsx, .xls',
          tipKey: 'task.import.report.fiveSync',
          templateKey: 'five_sync_host_url',
          data: {
            ...this.urlDetail,
            type: 3
          }
        })
      }
      // 基弱口令线核验报告
      if (
        ['4', '5'].includes(String(this.params.taskType)) &&
          this.$checkPermi(['risk:report:task:verification:uploadRiskScanZip']) &&
          this.params.taskStatus == '3'
      ) {
        value.push({
          label: '核验报告附件',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadRiskScanZip',
          accept: '.zip, .rar',
          tipKey: 'task.import.report.prompt',
          templateKey: 'risk.report.template',
          data: {
            ...this.urlDetail,
            type: 2
          }
        })
      }
      // 系统漏洞
      if (
        ['3', '9'].includes(String(this.params.taskType)) &&
          this.$checkPermi(['risk:report:task:uploadRiskSystemScanZip']) &&
          this.params.taskStatus == '3'
      ) {
        value.push({
          label: '核验报告附件',
          action: process.env.VUE_APP_BASE_API + '/risk/report/task/uploadRiskScanZip',
          accept: '.zip, .rar',
          tipKey: ['3'].includes(String(this.params.taskType)) ? 'task.import.report.prompt' : '',
          templateKey: 'risk.report.template',
          data: {
            ...this.urlDetail,
            type: 2
          }
        })
      }

      if (
        (['8'].includes(String(this.params.taskType)) && this.$checkPermi(['hidden:danger:upload:attachment'])) ||
        (['11'].includes(String(this.params.taskType)) && this.$checkPermi(['risk:taskSendInfo:image:upload']))
      ) {
        value.push({
          label: '运维任务附件',
          action: process.env.VUE_APP_BASE_API + '/common/uploadMinio',
          tipKey: '',
          templateKey: '',
          data: {
            ...this.urlDetail
          },
          onSuccess: async(response) => {
            if (response.code !== 200) {
              this.$message.warning(response.msg)
            }

            const res = await request({
              url: '/risk/report/url',
              method: 'post',
              data: {
                ...this.urlDetail,
                fileName: response.originalFilename,
                riskReportUrl: response.url
              }
            })

            this.onSuccess(res)
          }
        })
      }

      if (['14'].includes(String(this.params.taskType))) {
        let label = '合作伙伴自审计附件'

        if (['1'].includes(this.params.reportType)) label = '原始审计文件附件'

        const reportModel = (params = {}) => ({
          label: params.label || label,
          action: process.env.VUE_APP_BASE_API + '/common/uploadMinio',
          multiple: true,
          tipKey: '',
          templateKey: '',
          data: {
            ...this.urlDetail,
            ...(params.data || {})
          },
          onSuccess: async(response) => {
            console.log(response)
            if (response.code !== 200) {
              this.$message.warning(response.msg)
            }

            const res = await request({
              url: '/risk/report/url',
              method: 'post',
              data: {
                ...this.urlDetail,
                fileName: response.originalFilename,
                riskReportUrl: response.url,
                reportFileType: '5',
                reportType: this.params.reportType,
                ...(params.data || {})
              }
            })

            this.onSuccess(res)
          }
        })

        value.push(reportModel({ data: { auditReportType: '0' }}))

        if (!['1'].includes(this.params.reportType)) {
          value.push(reportModel({ label: '日志截图附件', data: { auditReportType: '1' }}))
        }
      }

      if (['17'].includes(String(this.params.taskType))) {
        const reportModel = (params = {}) => ({
          label: params.label || label,
          action: process.env.VUE_APP_BASE_API + '/common/uploadMinio',
          multiple: params.multiple,
          tipKey: params.tipKey,
          templateKey: '',
          data: {
            ...this.urlDetail,
            ...(params.data || {})
          },
          onSuccess: async(response) => {
            console.log(response)
            if (response.code !== 200) {
              this.$message.warning(response.msg)
            }

            const res = await request({
              url: '/risk/report/url',
              method: 'post',
              data: {
                ...this.urlDetail,
                fileName: response.originalFilename,
                riskReportUrl: response.url,
                // reportType: this.params.reportType,
                ...(params.data || {})
              }
            })

            this.onSuccess(res)
          }
        })

        if (this.$checkPermi(['risk:report:task:app:package'])) {
          value.push(reportModel({ label: 'APP安装包附件', tipKey: 'task.import.report.app', data: { reportFileType: '8', status: '1' }}))
        }

        if (['2'].includes(this.params.taskStatus) && this.$checkPermi(['risk:report:task:app:testReport'])) {
          value.push(reportModel({ label: 'APP个人合规信息检测报告', data: { reportFileType: '9', status: '4' }}))
        }
      }

      if (['19'].includes(String(this.params.taskType)) && this.$checkPermi(['risk:report:task:detour:strategy:upload'])) {
        value.push({
          label: '绕行报备附件',
          action: process.env.VUE_APP_BASE_API + '/risk/strategy/import',
          accept: '.xlsx, .xls',
          tipKey: '',
          templateKey: 'detour_file_address',
          data: {
            ...this.urlDetail
          }
        })
      }

      return value
    }
  },
  methods: {
    beforeUpload() {
      this.uploadLoading = true

      clearTimeout(this.uploadTimer)

      this.uploadTimer = setTimeout(() => {
        this.uploadLoading = false
      }, 30 * 60 * 1000)
    },
    async getTips() {
      const res = await Promise.allSettled([
        ...this.tipModel.map((item) => this.getConfigKey(item))
      ])

      this.tips = {
        ...this.tipModel.reduce((obj, item, index) => {
          obj[item] = res?.[index]?.value?.msg
          return obj
        }, {})
      }
    },
    async open(args = {}) {
      this.visible = true
      this.params = args.params
      this.getTips()
    },
    close() {
      this.visible = false
    },
    onClosed() {
      this.uploadProps = this.$options.data().uploadProps
      this.params = this.$options.data().params
      this.uploadLoading = false
      this.clearFiles()
    },
    clearFiles() {
      this.$refs.uploadRef[0]?.clearFiles?.()
    },
    onSuccess(res) {
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.clearFiles()
      } else {
        this.$message.warning(res.msg)
      }

      this.uploadLoading = false

      this.$emit('success')

      this.$refs.urlIndexRef.getList()
    },
    async downloadTemplate(templateKey) {
      const res = await this.getConfigKey(templateKey)
      window.location.href = res.msg
    },
    onProgress(event) {
      this.loadingText = `正在上传中，请稍候...(${event.percent.toFixed(0)}%)`
    }
    // handleSubmit() {
    //   if (!this.$refs.uploadRef.uploadFiles?.length) {
    //     this.$message.warning('请先选择要上传的报告');
    //     return false;
    //   }

    //   this.uploadProps.isUploading = true;
    //   this.$refs.uploadRef.submit();

    //   this.uploadProps.loading = this.$loading({
    //     lock: true,
    //     text: 'Loading',
    //     spinner: 'el-icon-loading',
    //     background: 'rgba(0, 0, 0, 0)',
    //   });
    // },
  }
}
</script>

<style lang="scss" scoped>
</style>
